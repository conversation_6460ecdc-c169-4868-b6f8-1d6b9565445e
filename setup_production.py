#!/usr/bin/env python3
"""
Setup script to deploy the trained Nepali TTS model to production.
This script exports the model and sets up the API and frontend.
"""

import subprocess
import shutil
import os
from pathlib import Path

def export_model():
    """Export the checkpoint to ONNX format."""
    print("🔄 Exporting model to ONNX format...")
    
    # Run the export script
    result = subprocess.run(
        ["python3", "export_model.py"],
        cwd="finetune",
        capture_output=True,
        text=True
    )
    
    if result.returncode == 0:
        print("✅ Model exported successfully!")
        return True
    else:
        print(f"❌ Model export failed: {result.stderr}")
        return False

def setup_api():
    """Copy model files to API directory and start the API."""
    print("🚀 Setting up API...")
    
    # Paths
    model_source = Path("finetune/exported_model/nepali_tts.onnx")
    config_source = Path("finetune/exported_model/nepali_tts.onnx.json")
    api_dir = Path("piper_api")
    
    # Copy model files
    if model_source.exists() and config_source.exists():
        shutil.copy2(model_source, api_dir / "nepali_tts.onnx")
        shutil.copy2(config_source, api_dir / "nepali_tts.onnx.json")
        print("✅ Model files copied to API directory")
        return True
    else:
        print("❌ Model files not found!")
        return False

def start_api():
    """Start the API server."""
    print("🌐 Starting API server...")
    print("Run this command in a new terminal:")
    print("cd piper_api && python3 -m piper_api")
    print("API will be available at: http://localhost:8000")

def start_frontend():
    """Start the frontend."""
    print("🎨 Starting frontend...")
    print("Run this command in another terminal:")
    print("cd piper_frontend && npm start")
    print("Frontend will be available at: http://localhost:3000")

def main():
    """Main setup function."""
    print("🎯 Setting up Nepali TTS Production Environment")
    print("=" * 50)
    
    # Step 1: Export model
    if not export_model():
        print("💥 Setup failed at model export step!")
        return
    
    # Step 2: Setup API
    if not setup_api():
        print("💥 Setup failed at API setup step!")
        return
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Start the API server:")
    print("   cd piper_api && python3 -m piper_api")
    print("\n2. Start the frontend (in another terminal):")
    print("   cd piper_frontend && npm start")
    print("\n3. Open your browser to: http://localhost:3000")
    print("\n🔧 Your trained Nepali TTS model is now ready for production!")
    
    # Ask if user wants to start services
    start_services = input("\n🚀 Start API and frontend now? (y/n): ")
    if start_services.lower() == 'y':
        print("\n" + "="*50)
        start_api()
        print("\n" + "="*50)
        start_frontend()

if __name__ == "__main__":
    main()
