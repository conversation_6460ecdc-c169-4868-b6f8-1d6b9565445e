#!/usr/bin/env python3
"""
FastAPI server for Piper TTS using the official Piper implementation.
"""

import io
import logging
import wave
import asyncio
from pathlib import Path
from typing import Optional, List
from concurrent.futures import ThreadPoolExecutor

from fastapi import FastAPI, HTTPException, Query, BackgroundTasks
from fastapi.responses import Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

try:
    from .voice import PiperVoice
except ImportError:
    from voice import Piper<PERSON>oice

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Piper TTS API",
    description="Text-to-Speech API using official Piper implementation with speaker and speech rate control",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class VoiceManager:
    """Optimized voice manager for scalable inference"""
    def __init__(self):
        self.voice: Optional[PiperVoice] = None
        self.voice_config = {}
        self.is_loading = False

    def load_voice(self, model_path: str, config_path: Optional[str] = None, use_cuda: bool = False):
        """Load voice with optimization for inference"""
        if self.is_loading:
            return False

        self.is_loading = True
        try:
            # Load with optimized ONNX session options
            self.voice = PiperVoice.load(model_path, config_path=config_path, use_cuda=use_cuda)
            self.voice_config = {
                "model_path": model_path,
                "config_path": config_path or f"{model_path}.json",
                "use_cuda": use_cuda
            }
            logger.info(f"Voice loaded: speakers={self.voice.config.num_speakers}, sample_rate={self.voice.config.sample_rate}")
            return True
        except Exception as e:
            logger.error(f"Failed to load voice: {e}")
            return False
        finally:
            self.is_loading = False

    def synthesize_audio(self, text: str, speaker_id: Optional[int] = None,
                        length_scale: Optional[float] = None, noise_scale: Optional[float] = None,
                        noise_w: Optional[float] = None, sentence_silence: float = 0.0) -> bytes:
        """Optimized synthesis function"""
        if not self.voice:
            raise ValueError("No voice loaded")

        with io.BytesIO() as wav_io:
            with wave.open(wav_io, "wb") as wav_file:
                self.voice.synthesize(
                    text=text,
                    wav_file=wav_file,
                    speaker_id=speaker_id,
                    length_scale=length_scale,
                    noise_scale=noise_scale,
                    noise_w=noise_w,
                    sentence_silence=sentence_silence
                )
            return wav_io.getvalue()

# Global voice manager and thread pool for CPU-bound inference
voice_manager = VoiceManager()
executor = ThreadPoolExecutor(max_workers=2)  # Limit to prevent memory issues with ONNX

# Pydantic models for request/response
class TTSRequest(BaseModel):
    text: str = Field(..., description="Text to synthesize", min_length=1, max_length=2000)
    speaker_id: Optional[int] = Field(None, description="Speaker ID", ge=0)
    speech_rate: Optional[float] = Field(1.0, description="Speech rate (0.5 = slower, 2.0 = faster)", gt=0.1, le=3.0)
    noise_scale: Optional[float] = Field(None, description="Generator noise (0.0-1.0)")
    noise_w: Optional[float] = Field(None, description="Phoneme width noise (0.0-1.0)")
    sentence_silence: Optional[float] = Field(0.0, description="Seconds of silence after each sentence", ge=0.0, le=2.0)

class VoiceInfo(BaseModel):
    num_speakers: int
    sample_rate: int
    espeak_voice: str
    phoneme_type: str
    length_scale: float
    noise_scale: float
    noise_w: float

class SpeakerInfo(BaseModel):
    speaker_id: int
    total_speakers: int

# Remove old load_voice function - now handled by VoiceManager

@app.on_event("startup")
async def startup_event():
    """Initialize the voice on startup."""
    # Try to load a default model if available
    model_paths = [
        "nepali_tts.onnx",  # Your trained Nepali model first
        "models/nepali_tts.onnx",
        "models/ne_NP-trained-medium.onnx",
        "models/ne_NP-google-medium.onnx",
        "ne_NP-google-medium.onnx",
        "voice.onnx"
    ]

    for model_path in model_paths:
        if Path(model_path).exists():
            logger.info(f"Found model: {model_path}")
            if voice_manager.load_voice(model_path):
                logger.info(f"Successfully loaded model: {model_path}")
                break
            else:
                logger.warning(f"Failed to load model: {model_path}")
    else:
        logger.warning("No voice model found. Use /load_voice endpoint to load a model.")

@app.get("/", summary="Health check")
async def root():
    """Health check endpoint."""
    return {
        "message": "Piper TTS API is running",
        "status": "healthy",
        "voice_loaded": voice_manager.voice is not None
    }

@app.post("/load_voice", summary="Load a voice model")
async def load_voice_endpoint(
    model_path: str = Query(..., description="Path to ONNX model file"),
    config_path: Optional[str] = Query(None, description="Path to config JSON file"),
    use_cuda: bool = Query(False, description="Use CUDA acceleration")
):
    """Load a voice model from the specified path."""
    if not Path(model_path).exists():
        raise HTTPException(status_code=404, detail=f"Model file not found: {model_path}")

    if config_path and not Path(config_path).exists():
        raise HTTPException(status_code=404, detail=f"Config file not found: {config_path}")

    success = voice_manager.load_voice(model_path, config_path, use_cuda)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to load voice model")

    return {
        "message": "Voice loaded successfully",
        "model_path": model_path,
        "config_path": config_path or f"{model_path}.json",
        "voice_info": await get_voice_info()
    }

@app.get("/voice/info", response_model=VoiceInfo, summary="Get voice information")
async def get_voice_info():
    """Get information about the loaded voice."""
    if voice_manager.voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")

    return VoiceInfo(
        num_speakers=voice_manager.voice.config.num_speakers,
        sample_rate=voice_manager.voice.config.sample_rate,
        espeak_voice=voice_manager.voice.config.espeak_voice,
        phoneme_type=voice_manager.voice.config.phoneme_type.value,
        length_scale=voice_manager.voice.config.length_scale,
        noise_scale=voice_manager.voice.config.noise_scale,
        noise_w=voice_manager.voice.config.noise_w
    )

@app.get("/speakers", summary="Get speaker information")
async def get_speakers():
    """Get information about available speakers."""
    if voice_manager.voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")

    return {
        "num_speakers": voice_manager.voice.config.num_speakers,
        "speakers": [{"speaker_id": i} for i in range(voice_manager.voice.config.num_speakers)]
    }

@app.post("/synthesize", summary="Synthesize speech from text")
async def synthesize_speech(request: TTSRequest):
    """
    Synthesize speech from text with speaker and speech rate control.

    Returns WAV audio data.
    """
    if voice_manager.voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")

    try:
        logger.info(f"Synthesis request: text='{request.text[:50]}...', speaker_id={request.speaker_id}")

        # Validate speaker ID
        if request.speaker_id is not None:
            if request.speaker_id < 0 or request.speaker_id >= voice_manager.voice.config.num_speakers:
                raise HTTPException(
                    status_code=400,
                    detail=f"Speaker ID must be between 0 and {voice_manager.voice.config.num_speakers - 1}"
                )

        # Convert speech_rate to length_scale (inverse relationship)
        length_scale = 1.0 / request.speech_rate if request.speech_rate else None
        logger.info(f"Synthesis parameters: length_scale={length_scale}, noise_scale={request.noise_scale}")

        # Run synthesis in thread pool for better concurrency
        loop = asyncio.get_event_loop()
        wav_data = await loop.run_in_executor(
            executor,
            voice_manager.synthesize_audio,
            request.text,
            request.speaker_id,
            length_scale,
            request.noise_scale,
            request.noise_w,
            request.sentence_silence or 0.0
        )

        logger.info(f"Generated audio: {len(wav_data)} bytes")

        # Return WAV file
        return Response(
            content=wav_data,
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=speech.wav",
                "Content-Length": str(len(wav_data))
            }
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error during synthesis: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during synthesis")

@app.get("/synthesize", summary="Synthesize speech from text (GET)")
async def synthesize_speech_get(
    text: str = Query(..., description="Text to synthesize", min_length=1, max_length=2000),
    speaker_id: Optional[int] = Query(None, description="Speaker ID", ge=0),
    speech_rate: Optional[float] = Query(1.0, description="Speech rate (0.5 = slower, 2.0 = faster)", gt=0.1, le=3.0),
    noise_scale: Optional[float] = Query(None, description="Generator noise (0.0-1.0)", ge=0.0, le=1.0),
    noise_w: Optional[float] = Query(None, description="Phoneme width noise (0.0-1.0)", ge=0.0, le=1.0),
    sentence_silence: Optional[float] = Query(0.0, description="Seconds of silence after each sentence", ge=0.0, le=2.0)
):
    """
    Synthesize speech from text using GET parameters.
    
    Returns WAV audio data.
    """
    request = TTSRequest(
        text=text,
        speaker_id=speaker_id,
        speech_rate=speech_rate,
        noise_scale=noise_scale,
        noise_w=noise_w,
        sentence_silence=sentence_silence
    )
    return await synthesize_speech(request)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
