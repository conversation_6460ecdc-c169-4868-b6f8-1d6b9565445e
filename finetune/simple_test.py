#!/usr/bin/env python3
"""
Simple test script for the Nepali TTS checkpoint.
Uses piper_train.infer to generate audio from text.
"""

import json
import subprocess
from pathlib import Path

def create_test_jsonl():
    """Create a test JSONL file with Nepali text."""
    
    # Sample Nepali texts
    test_texts = [
        "नमस्कार, म नेपाली बोल्छु।",
        "यो एक परीक्षण हो।", 
        "काठमाडौं नेपालको राजधानी हो।"
    ]
    
    # Create output directory
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    # Create JSONL file
    test_file = output_dir / "test_sentences.jsonl"
    
    with open(test_file, 'w', encoding='utf-8') as f:
        for text in test_texts:
            # Create entry with text only - phoneme_ids will be generated
            entry = {
                "text": text,
                "speaker_id": 0
            }
            json.dump(entry, f, ensure_ascii=False)
            f.write('\n')
    
    return test_file, output_dir

def run_inference():
    """Run inference on the test file."""
    
    # Create test file
    test_file, output_dir = create_test_jsonl()
    
    # Find the latest checkpoint
    checkpoint_dir = Path("/home/<USER>/Documents/personal/test/finetune/training_output/lightning_logs/version_0/checkpoints")
    checkpoints = list(checkpoint_dir.glob("*.ckpt"))
    
    if not checkpoints:
        print("❌ No checkpoints found!")
        return False
    
    latest_checkpoint = max(checkpoints, key=lambda x: x.stat().st_mtime)
    print(f"🔍 Using checkpoint: {latest_checkpoint}")
    print(f"📝 Test file: {test_file}")
    print(f"📁 Output directory: {output_dir}")
    
    # Run inference
    cmd = [
        "python3", "-m", "piper_train.infer",
        "--sample-rate", "22050",
        "--checkpoint", str(latest_checkpoint),
        "--output-dir", str(output_dir)
    ]
    
    print(f"\n🚀 Running inference...")
    print("Command:", " ".join(cmd))
    
    try:
        # Pipe the test file to the inference command
        with open(test_file, 'r', encoding='utf-8') as f:
            result = subprocess.run(
                cmd,
                stdin=f,
                capture_output=True,
                text=True,
                timeout=60,
                cwd="/home/<USER>/Documents/personal/test/finetune/piper/src/python"
            )
        
        if result.returncode == 0:
            print("✅ Inference completed successfully!")
            print("Output:", result.stdout)
            
            # List generated files
            wav_files = list(output_dir.glob("*.wav"))
            if wav_files:
                print(f"\n🎵 Generated {len(wav_files)} audio files:")
                for wav_file in wav_files:
                    print(f"   - {wav_file}")
                print(f"\nTo play: aplay {output_dir}/*.wav")
            else:
                print("⚠️  No WAV files found in output directory")
            
            return True
        else:
            print("❌ Inference failed!")
            print("Error:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Inference timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Nepali TTS model checkpoint...")
    success = run_inference()
    
    if success:
        print("\n🎉 Test completed! Check the audio files.")
    else:
        print("\n💥 Test failed. The model might need more training.")
