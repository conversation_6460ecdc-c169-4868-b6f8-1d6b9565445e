#!/usr/bin/env python3
"""
Export the trained Nepali TTS checkpoint to ONNX format for production use.
"""

import subprocess
import shutil
from pathlib import Path

def export_to_onnx():
    """Export the checkpoint to ONNX format."""
    
    # Paths
    checkpoint_dir = Path("/home/<USER>/Documents/personal/test/finetune/training_output/lightning_logs/version_0/checkpoints")
    config_file = Path("/home/<USER>/Documents/personal/test/finetune/training_output/config.json")
    output_dir = Path("/home/<USER>/Documents/personal/test/finetune/exported_model")
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Find the latest checkpoint
    checkpoints = list(checkpoint_dir.glob("*.ckpt"))
    if not checkpoints:
        print("❌ No checkpoints found!")
        return False
    
    latest_checkpoint = max(checkpoints, key=lambda x: x.stat().st_mtime)
    print(f"🔍 Using checkpoint: {latest_checkpoint}")
    
    # Output files
    onnx_file = output_dir / "nepali_tts.onnx"
    config_output = output_dir / "nepali_tts.onnx.json"
    
    print(f"📦 Exporting to: {onnx_file}")
    print(f"📄 Config file: {config_output}")
    
    try:
        # Export to ONNX
        cmd = [
            "python3", "-m", "piper_train.export_onnx",
            str(latest_checkpoint),
            str(onnx_file)
        ]
        
        print("🚀 Running export command...")
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd="/home/<USER>/Documents/personal/test/finetune/piper/src/python"
        )
        
        if result.returncode != 0:
            print(f"❌ Export failed: {result.stderr}")
            return False
        
        print("✅ ONNX export completed!")
        
        # Copy config file
        if config_file.exists():
            shutil.copy2(config_file, config_output)
            print(f"✅ Config file copied to: {config_output}")
        else:
            print("⚠️  Config file not found!")
        
        # Verify files exist
        if onnx_file.exists() and config_output.exists():
            print(f"\n🎉 Export successful!")
            print(f"📁 Model files:")
            print(f"   - ONNX model: {onnx_file}")
            print(f"   - Config: {config_output}")
            print(f"   - Size: {onnx_file.stat().st_size / (1024*1024):.1f} MB")
            
            return True
        else:
            print("❌ Export files not found!")
            return False
            
    except Exception as e:
        print(f"❌ Export error: {e}")
        return False

def copy_to_api():
    """Copy the exported model to the API directory."""
    
    source_dir = Path("/home/<USER>/Documents/personal/test/finetune/exported_model")
    api_dir = Path("/home/<USER>/Documents/personal/test/piper_api")
    
    if not api_dir.exists():
        print("⚠️  API directory not found. Creating it...")
        api_dir.mkdir(exist_ok=True)
    
    # Copy model files
    onnx_source = source_dir / "nepali_tts.onnx"
    config_source = source_dir / "nepali_tts.onnx.json"
    
    if onnx_source.exists() and config_source.exists():
        shutil.copy2(onnx_source, api_dir / "nepali_tts.onnx")
        shutil.copy2(config_source, api_dir / "nepali_tts.onnx.json")
        
        print(f"✅ Model files copied to API directory: {api_dir}")
        return True
    else:
        print("❌ Source model files not found!")
        return False

if __name__ == "__main__":
    print("🔄 Exporting Nepali TTS model to ONNX...")
    
    if export_to_onnx():
        print("\n📋 Next steps:")
        print("1. Copy model to API directory")
        print("2. Update API to use the new model")
        print("3. Test the API with the new model")
        
        copy_choice = input("\n📁 Copy model to API directory? (y/n): ")
        if copy_choice.lower() == 'y':
            copy_to_api()
    else:
        print("\n💥 Export failed!")
