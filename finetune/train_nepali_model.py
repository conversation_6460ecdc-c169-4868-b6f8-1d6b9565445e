#!/usr/bin/env python3
"""
Train Nepali TTS model using Piper.
This script starts training from scratch with the preprocessed Nepali dataset.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_training():
    """Run the Piper training process."""
    
    # Paths
    dataset_dir = Path("/home/<USER>/Documents/personal/test/finetune/training_output")
    
    # Check if required files exist
    config_file = dataset_dir / "config.json"
    dataset_file = dataset_dir / "dataset.jsonl"
    
    if not config_file.exists():
        print(f"Error: {config_file} not found!")
        return False
        
    if not dataset_file.exists():
        print(f"Error: {dataset_file} not found!")
        return False
    
    print(f"Dataset directory: {dataset_dir}")
    print(f"Config file: {config_file}")
    print(f"Dataset file: {dataset_file}")
    
    # Training command
    cmd = [
        "python3", "-m", "piper_train",
        "--dataset-dir", str(dataset_dir),
        "--accelerator", "gpu",
        "--devices", "1",
        "--batch-size", "16",  # Reduced for RTX 3060
        "--validation-split", "0.1",  # 10% for validation
        "--num-test-examples", "50",
        "--max_epochs", "1000",  # Start with fewer epochs for testing
        "--checkpoint-epochs", "10",  # Save checkpoint every 10 epochs
        "--precision", "16-mixed",  # Mixed precision for better memory usage
        "--quality", "medium",  # Medium quality for faster training
        "--max-phoneme-ids", "300"  # Limit sentence length for memory
    ]
    
    print("Starting training with command:")
    print(" ".join(cmd))
    print("\n" + "="*50)
    
    try:
        # Run the training
        result = subprocess.run(cmd, check=True, cwd="/home/<USER>/Documents/personal/test/finetune/piper/src/python")
        print("\n" + "="*50)
        print("✅ Training completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Training failed with error: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ Training interrupted by user")
        return False

if __name__ == "__main__":
    print("🚀 Starting Nepali TTS model training...")
    success = run_training()
    
    if success:
        print("\n🎉 Training completed! Check the lightning_logs directory for checkpoints.")
        print("You can monitor training progress with:")
        print("tensorboard --logdir /home/<USER>/Documents/personal/test/finetune/training_output/lightning_logs")
    else:
        print("\n💥 Training failed. Check the error messages above.")
        sys.exit(1)
