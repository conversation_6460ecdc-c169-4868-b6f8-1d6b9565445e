#!/usr/bin/env python3
"""
Test the exported ONNX model directly to verify it's working correctly.
"""

import json
import wave
import numpy as np
import onnxruntime
from pathlib import Path
from piper_phonemize import phonemize_espeak

def test_onnx_model():
    """Test the ONNX model directly."""
    
    # Paths
    model_path = Path("exported_model/nepali_tts.onnx")
    config_path = Path("exported_model/nepali_tts.onnx.json")
    
    print(f"🔍 Testing ONNX model: {model_path}")
    print(f"📄 Config file: {config_path}")
    
    # Check if files exist
    if not model_path.exists():
        print(f"❌ Model file not found: {model_path}")
        return False
        
    if not config_path.exists():
        print(f"❌ Config file not found: {config_path}")
        return False
    
    # Load config
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"📊 Model info:")
    print(f"   - Sample rate: {config.get('audio', {}).get('sample_rate', 'unknown')}")
    print(f"   - Speakers: {config.get('num_speakers', 'unknown')}")
    print(f"   - Language: {config.get('espeak', {}).get('voice', 'unknown')}")
    
    # Test with a longer sentence
    test_text = "नमस्कार, म एक कृत्रिम बुद्धिमत्ता आधारित आवाज संश्लेषण प्रणाली हुँ। म नेपाली भाषामा बोल्न सक्छु र विभिन्न प्रकारका पाठहरूलाई प्राकृतिक आवाजमा रूपान्तरण गर्न सक्छु।"
    print(f"🎯 Test text: '{test_text}'")
    
    try:
        # Load ONNX model
        print("🔄 Loading ONNX model...")
        session = onnxruntime.InferenceSession(str(model_path))
        
        # Get model inputs/outputs info
        input_names = [inp.name for inp in session.get_inputs()]
        output_names = [out.name for out in session.get_outputs()]
        
        print(f"📥 Model inputs: {input_names}")
        print(f"📤 Model outputs: {output_names}")
        
        # Phonemize text
        print("🔄 Phonemizing text...")
        espeak_voice = config.get('espeak', {}).get('voice', 'ne')
        phonemes = phonemize_espeak(test_text, espeak_voice)

        print(f"🔤 Phonemes: {phonemes}")

        if len(phonemes) == 0 or len(phonemes[0]) == 0:
            print("❌ No phonemes generated! Check espeak voice or text.")
            return False

        # Get phoneme to ID mapping
        phoneme_id_map = config.get('phoneme_id_map', {})
        if not phoneme_id_map:
            print("❌ No phoneme_id_map found in config!")
            return False

        print(f"📚 Available phonemes: {len(phoneme_id_map)} phonemes")

        # Convert phonemes to IDs
        phoneme_ids = []
        for phoneme in phonemes[0]:  # phonemes is a list of lists
            if phoneme in phoneme_id_map:
                pid = phoneme_id_map[phoneme]
                # Handle nested lists in phoneme_id_map
                if isinstance(pid, list):
                    phoneme_ids.extend(pid)
                else:
                    phoneme_ids.append(pid)
            else:
                print(f"⚠️  Unknown phoneme: '{phoneme}', skipping...")

        print(f"🔢 Phoneme IDs: {phoneme_ids}")
        print(f"📏 Phoneme ID count: {len(phoneme_ids)}")

        if len(phoneme_ids) == 0:
            print("❌ No valid phoneme IDs generated!")
            return False

        # Add BOS and EOS tokens if they exist
        bos_id = phoneme_id_map.get('^', None)
        eos_id = phoneme_id_map.get('$', None)

        if bos_id is not None:
            if isinstance(bos_id, list):
                phoneme_ids = bos_id + phoneme_ids
            else:
                phoneme_ids = [bos_id] + phoneme_ids
        if eos_id is not None:
            if isinstance(eos_id, list):
                phoneme_ids = phoneme_ids + eos_id
            else:
                phoneme_ids = phoneme_ids + [eos_id]

        print(f"🔢 Final phoneme IDs (with BOS/EOS): {phoneme_ids}")

        # Prepare input
        phoneme_ids = np.array(phoneme_ids, dtype=np.int64).reshape(1, -1)
        phoneme_lengths = np.array([phoneme_ids.shape[1]], dtype=np.int64)
        
        # Prepare scales (use defaults from config)
        noise_scale = np.array([config.get('inference', {}).get('noise_scale', 0.667)], dtype=np.float32)
        length_scale = np.array([config.get('inference', {}).get('length_scale', 1.0)], dtype=np.float32)
        noise_w = np.array([config.get('inference', {}).get('noise_w', 0.8)], dtype=np.float32)
        
        print(f"🎛️  Inference parameters:")
        print(f"   - Noise scale: {noise_scale[0]}")
        print(f"   - Length scale: {length_scale[0]}")
        print(f"   - Noise W: {noise_w[0]}")
        
        # Run inference
        print("🚀 Running ONNX inference...")
        
        # Prepare inputs based on model signature
        inputs = {
            'input': phoneme_ids,
            'input_lengths': phoneme_lengths,
            'scales': np.array([noise_scale[0], length_scale[0], noise_w[0]], dtype=np.float32)
        }
        
        # Try different input combinations if the above fails
        try:
            outputs = session.run(output_names, inputs)
        except Exception as e:
            print(f"⚠️  First input format failed: {e}")
            print("🔄 Trying alternative input format...")
            
            # Alternative format
            inputs = {
                'input': phoneme_ids,
                'input_lengths': phoneme_lengths,
                'noise_scale': noise_scale,
                'length_scale': length_scale,
                'noise_w': noise_w
            }
            
            try:
                outputs = session.run(output_names, inputs)
            except Exception as e2:
                print(f"❌ Second input format also failed: {e2}")
                print("🔍 Available inputs:")
                for inp in session.get_inputs():
                    print(f"   - {inp.name}: {inp.shape} ({inp.type})")
                return False
        
        # Get audio output
        audio = outputs[0]
        print(f"🎵 Generated audio shape: {audio.shape}")
        print(f"📊 Audio stats: min={audio.min():.4f}, max={audio.max():.4f}, mean={audio.mean():.4f}")

        if audio.size == 0:
            print("❌ Generated audio is empty!")
            return False

        # Flatten the audio array - remove extra dimensions
        audio = audio.squeeze()  # Remove dimensions of size 1
        print(f"🎵 Flattened audio shape: {audio.shape}")

        if audio.ndim > 1:
            print("⚠️  Audio still has multiple dimensions, taking first channel")
            audio = audio[0] if audio.shape[0] < audio.shape[1] else audio[:, 0]

        print(f"🎵 Final audio shape: {audio.shape}")
        print(f"📊 Final audio stats: min={audio.min():.4f}, max={audio.max():.4f}, mean={audio.mean():.4f}")

        # Convert to int16 and save
        audio_int16 = (audio * 32767).astype(np.int16)

        output_file = Path("test_onnx_output.wav")
        sample_rate = config.get('audio', {}).get('sample_rate', 22050)

        with wave.open(str(output_file), 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())

        print(f"✅ Audio saved to: {output_file}")
        print(f"📏 Audio samples: {len(audio_int16)}")
        print(f"📏 Duration: {len(audio_int16) / sample_rate:.2f} seconds")
        print(f"🎵 To play: aplay {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during ONNX inference: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing exported ONNX model directly...")
    success = test_onnx_model()
    
    if success:
        print("\n🎉 ONNX model test successful!")
    else:
        print("\n💥 ONNX model test failed!")
        print("The issue might be:")
        print("1. Model export problem")
        print("2. Phonemization issue")
        print("3. Input format mismatch")
        print("4. Model architecture issue")
