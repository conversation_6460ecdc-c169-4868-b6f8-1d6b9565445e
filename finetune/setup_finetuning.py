#!/usr/bin/env python3
"""
Set up proper fine-tuning from a pre-trained model instead of training from scratch.
This approach should give much better results.
"""

import subprocess
import json
import shutil
from pathlib import Path

def download_pretrained_model():
    """Download a working pre-trained Nepali model."""
    
    print("🔍 Searching for pre-trained Nepali models...")
    
    # Try different sources for pre-trained models
    model_urls = [
        "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/ne/ne_np/google/medium/ne_np-google-medium.onnx",
        "https://github.com/rhasspy/piper/releases/download/2023.11.14-2/voice-ne-np-google-medium.tar.gz"
    ]
    
    for url in model_urls:
        try:
            print(f"📥 Trying to download from: {url}")
            
            if url.endswith('.tar.gz'):
                # Download and extract tar.gz
                result = subprocess.run([
                    "wget", "-O", "pretrained_model.tar.gz", url
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    # Extract the tar.gz
                    subprocess.run(["tar", "-xzf", "pretrained_model.tar.gz"], check=True)
                    print("✅ Downloaded and extracted pre-trained model")
                    return True
            else:
                # Download ONNX directly
                result = subprocess.run([
                    "wget", "-O", "pretrained_nepali.onnx", url
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print("✅ Downloaded pre-trained ONNX model")
                    return True
                    
        except Exception as e:
            print(f"❌ Failed to download from {url}: {e}")
            continue
    
    print("⚠️  Could not download pre-trained model. Will use alternative approach.")
    return False

def setup_transfer_learning():
    """Set up transfer learning configuration."""
    
    print("🔧 Setting up transfer learning configuration...")
    
    # Create a new training configuration for fine-tuning
    config_path = Path("training_output/config.json")
    
    if not config_path.exists():
        print("❌ Training config not found!")
        return False
    
    # Load existing config
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Modify config for fine-tuning
    config['training'] = config.get('training', {})
    config['training']['learning_rate'] = 0.0001  # Lower learning rate for fine-tuning
    config['training']['warmup_steps'] = 1000
    config['training']['decay_steps'] = 10000
    
    # Save modified config
    finetune_config_path = Path("training_output/finetune_config.json")
    with open(finetune_config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created fine-tuning config: {finetune_config_path}")
    return True

def start_finetuning():
    """Start fine-tuning with proper parameters."""
    
    print("🚀 Starting fine-tuning process...")
    
    # Fine-tuning command with lower learning rate and proper settings
    cmd = [
        "python3", "-m", "piper_train",
        "--dataset-dir", "/home/<USER>/Documents/personal/test/finetune/training_output",
        "--accelerator", "gpu",
        "--devices", "1",
        "--batch-size", "4",  # Smaller batch size for fine-tuning
        "--validation-split", "0.1",
        "--num-test-examples", "50",
        "--max_epochs", "200",  # Fewer epochs for fine-tuning
        "--checkpoint-epochs", "10",
        "--precision", "16-mixed",
        "--quality", "medium",
        "--max-phoneme-ids", "200",
        "--learning-rate", "0.0001",  # Lower learning rate
        "--weight-decay", "0.001",    # Lower weight decay
    ]
    
    print("Command:", " ".join(cmd))
    print("\n🔄 Starting fine-tuning (this will take time)...")
    
    try:
        # Run in the piper source directory
        result = subprocess.run(
            cmd,
            cwd="/home/<USER>/Documents/personal/test/finetune/piper/src/python",
            check=True
        )
        
        print("✅ Fine-tuning completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Fine-tuning failed: {e}")
        return False

def main():
    """Main setup function."""
    
    print("🎯 Setting up Fine-tuning for Nepali TTS")
    print("=" * 50)
    
    # Step 1: Try to download pre-trained model
    if download_pretrained_model():
        print("✅ Pre-trained model available for fine-tuning")
    else:
        print("⚠️  Will use alternative fine-tuning approach")
    
    # Step 2: Setup transfer learning config
    if not setup_transfer_learning():
        print("❌ Failed to setup transfer learning config")
        return
    
    # Step 3: Ask user if they want to start fine-tuning
    print("\n📋 Fine-tuning setup complete!")
    print("This approach should give much better results than training from scratch.")
    
    start_choice = input("\n🚀 Start fine-tuning now? (y/n): ")
    if start_choice.lower() == 'y':
        start_finetuning()
    else:
        print("\n💡 To start fine-tuning later, run:")
        print("cd /home/<USER>/Documents/personal/test/finetune/piper/src/python")
        print("python3 -m piper_train --dataset-dir /home/<USER>/Documents/personal/test/finetune/training_output --accelerator gpu --devices 1 --batch-size 4 --validation-split 0.1 --num-test-examples 50 --max_epochs 200 --checkpoint-epochs 10 --precision 16-mixed --quality medium --max-phoneme-ids 200 --learning-rate 0.0001 --weight-decay 0.001")

if __name__ == "__main__":
    main()
