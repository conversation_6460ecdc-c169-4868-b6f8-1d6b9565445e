#!/usr/bin/env python3
"""
Test the trained Nepali TTS checkpoint without interfering with ongoing training.
This script uses minimal GPU memory and can run alongside training.
"""

import json
import torch
import subprocess
import sys
from pathlib import Path

def test_checkpoint():
    """Test the latest checkpoint with some sample Nepali text."""
    
    # Paths
    training_dir = Path("/home/<USER>/Documents/personal/test/finetune/training_output")
    checkpoint_dir = training_dir / "lightning_logs" / "version_0" / "checkpoints"
    config_file = training_dir / "config.json"
    
    # Find the latest checkpoint
    checkpoints = list(checkpoint_dir.glob("*.ckpt"))
    if not checkpoints:
        print("❌ No checkpoints found!")
        return False
    
    latest_checkpoint = max(checkpoints, key=lambda x: x.stat().st_mtime)
    print(f"🔍 Using checkpoint: {latest_checkpoint}")
    
    # Sample Nepali texts to test
    test_texts = [
        "नमस्कार, म नेपाली बोल्छु।",
        "यो एक परीक्षण हो।",
        "काठमाडौं नेपालको राजधानी हो।",
        "धन्यवाद।"
    ]
    
    # Create output directory
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    print(f"🎯 Testing {len(test_texts)} sample texts...")
    print(f"📁 Output directory: {output_dir.absolute()}")
    
    success_count = 0
    
    for i, text in enumerate(test_texts):
        try:
            print(f"\n🔊 Testing text {i+1}: '{text}'")
            
            # Create a simple JSONL input for piper_train.infer
            test_input = {
                "text": text,
                "phoneme_ids": [],  # Will be generated by the inference script
                "speaker_id": 0
            }
            
            # Write test input to temporary file
            temp_input = output_dir / f"test_input_{i+1}.jsonl"
            with open(temp_input, 'w', encoding='utf-8') as f:
                json.dump(test_input, f, ensure_ascii=False)
                f.write('\n')
            
            # Output file
            output_wav = output_dir / f"test_output_{i+1}.wav"
            
            # Run inference with minimal GPU usage
            cmd = [
                "python3", "-m", "piper_train.infer",
                "--sample-rate", "22050",
                "--checkpoint", str(latest_checkpoint),
                "--output-dir", str(output_dir),
                "--text", text
            ]
            
            print(f"   Running inference...")
            
            # Use subprocess with timeout to avoid hanging
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=30,
                cwd="/home/<USER>/Documents/personal/test/finetune/piper/src/python"
            )
            
            if result.returncode == 0:
                print(f"   ✅ Generated: {output_wav}")
                success_count += 1
            else:
                print(f"   ❌ Failed: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ Timeout - inference took too long")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n📊 Results: {success_count}/{len(test_texts)} successful")
    
    if success_count > 0:
        print(f"\n🎉 Success! Audio files generated in: {output_dir.absolute()}")
        print("You can play them with: aplay test_output/test_output_*.wav")
        return True
    else:
        print("\n💥 No audio files were generated successfully.")
        return False

if __name__ == "__main__":
    print("🧪 Testing Nepali TTS checkpoint...")
    print("⚠️  This will use minimal GPU memory and won't interfere with training.")
    
    # Set minimal GPU memory usage
    import os
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # Use same GPU but minimal memory
    
    success = test_checkpoint()
    
    if success:
        print("\n✨ Checkpoint test completed successfully!")
    else:
        print("\n🔧 Checkpoint test failed. The model might need more training.")
